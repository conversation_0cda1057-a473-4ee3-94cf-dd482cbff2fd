# advanced_creature_nn.py
# Modern neural network architecture for creature AI

import numpy as np
import random
from typing import List, Tu<PERSON>, Dict, Optional
from collections import deque
import pickle

class AdvancedCreatureNeuralNetwork:
    """
    Advanced neural network with modern RL techniques:
    - Actor-Critic architecture
    - LSTM for memory
    - Attention mechanism
    - Curiosity-driven exploration
    - Multi-head outputs
    - Batch normalization
    """
    
    def __init__(self,
                 input_size: int = 24,  # Enhanced state representation (24 features)
                 action_count: int = 5,
                 vocabulary_size: int = 100,
                 hidden_size: int = 128,  # Much larger
                 lstm_size: int = 64,
                 attention_heads: int = 4,
                 learning_rate: float = 0.0003,
                 gamma: float = 0.99,
                 gae_lambda: float = 0.95):
        
        self.input_size = input_size
        self.action_count = action_count
        self.vocabulary_size = vocabulary_size
        self.hidden_size = hidden_size
        self.lstm_size = lstm_size
        self.attention_heads = attention_heads
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        
        # Advanced memory systems
        self.episodic_memory = deque(maxlen=1000)  # Long-term memory
        self.working_memory = deque(maxlen=50)     # Short-term memory
        self.lstm_hidden = np.zeros((1, lstm_size))
        self.lstm_cell = np.zeros((1, lstm_size))
        
        # Curiosity and exploration
        self.intrinsic_motivation = 0.0
        self.exploration_bonus = 0.1
        self.state_visit_counts = {}
        
        # Initialize advanced architecture
        self._initialize_advanced_network()
        
        # Performance tracking
        self.episode_rewards = deque(maxlen=100)
        self.learning_progress = deque(maxlen=1000)
        
    def _initialize_advanced_network(self):
        """Initialize advanced network architecture."""
        
        # Input processing layers
        self.input_norm = self._create_batch_norm(self.input_size)
        self.input_weights = self._xavier_init(self.input_size, self.hidden_size)
        self.input_bias = np.zeros((1, self.hidden_size))
        
        # LSTM layers for temporal processing
        self.lstm_weights_input = self._xavier_init(self.hidden_size, 4 * self.lstm_size)
        self.lstm_weights_hidden = self._xavier_init(self.lstm_size, 4 * self.lstm_size)
        self.lstm_bias = np.zeros((1, 4 * self.lstm_size))
        
        # Multi-head attention
        self.attention_weights = []
        for _ in range(self.attention_heads):
            self.attention_weights.append({
                'query': self._xavier_init(self.lstm_size, self.lstm_size // self.attention_heads),
                'key': self._xavier_init(self.lstm_size, self.lstm_size // self.attention_heads),
                'value': self._xavier_init(self.lstm_size, self.lstm_size // self.attention_heads)
            })
        
        # Actor network (policy)
        self.actor_hidden1 = self._xavier_init(self.lstm_size, self.hidden_size)
        self.actor_bias1 = np.zeros((1, self.hidden_size))
        self.actor_hidden2 = self._xavier_init(self.hidden_size, self.hidden_size // 2)
        self.actor_bias2 = np.zeros((1, self.hidden_size // 2))
        
        # Action and vocabulary outputs
        self.action_output = self._xavier_init(self.hidden_size // 2, self.action_count)
        self.action_output_bias = np.zeros((1, self.action_count))
        self.vocab_output = self._xavier_init(self.hidden_size // 2, self.vocabulary_size)
        self.vocab_output_bias = np.zeros((1, self.vocabulary_size))
        
        # Critic network (value estimation)
        self.critic_hidden1 = self._xavier_init(self.lstm_size, self.hidden_size)
        self.critic_bias1 = np.zeros((1, self.hidden_size))
        self.critic_hidden2 = self._xavier_init(self.hidden_size, self.hidden_size // 2)
        self.critic_bias2 = np.zeros((1, self.hidden_size // 2))
        self.value_output = self._xavier_init(self.hidden_size // 2, 1)
        self.value_output_bias = np.zeros((1, 1))
        
        # Curiosity network (for intrinsic motivation)
        self.curiosity_encoder = self._xavier_init(self.input_size, self.hidden_size // 2)
        self.curiosity_bias = np.zeros((1, self.hidden_size // 2))
        self.forward_model = self._xavier_init(self.hidden_size // 2 + self.action_count, self.hidden_size // 2)
        self.forward_bias = np.zeros((1, self.hidden_size // 2))
        
    def _xavier_init(self, fan_in: int, fan_out: int) -> np.ndarray:
        """Xavier/Glorot initialization."""
        limit = np.sqrt(6.0 / (fan_in + fan_out))
        return np.random.uniform(-limit, limit, (fan_in, fan_out))
    
    def _create_batch_norm(self, size: int) -> Dict:
        """Create batch normalization parameters."""
        return {
            'gamma': np.ones((1, size)),
            'beta': np.zeros((1, size)),
            'running_mean': np.zeros((1, size)),
            'running_var': np.ones((1, size)),
            'momentum': 0.9
        }
    
    def _batch_normalize(self, x: np.ndarray, bn_params: Dict, training: bool = True) -> np.ndarray:
        """Apply batch normalization."""
        if training:
            mean = np.mean(x, axis=0, keepdims=True)
            var = np.var(x, axis=0, keepdims=True)
            
            # Update running statistics
            bn_params['running_mean'] = (bn_params['momentum'] * bn_params['running_mean'] + 
                                       (1 - bn_params['momentum']) * mean)
            bn_params['running_var'] = (bn_params['momentum'] * bn_params['running_var'] + 
                                      (1 - bn_params['momentum']) * var)
        else:
            mean = bn_params['running_mean']
            var = bn_params['running_var']
        
        x_norm = (x - mean) / np.sqrt(var + 1e-8)
        return bn_params['gamma'] * x_norm + bn_params['beta']
    
    def _lstm_forward(self, x: np.ndarray) -> np.ndarray:
        """LSTM forward pass for temporal processing."""
        # Combine input and hidden state
        combined = np.dot(x, self.lstm_weights_input) + np.dot(self.lstm_hidden, self.lstm_weights_hidden) + self.lstm_bias
        
        # Split into gates
        i_gate = self._sigmoid(combined[:, :self.lstm_size])  # Input gate
        f_gate = self._sigmoid(combined[:, self.lstm_size:2*self.lstm_size])  # Forget gate
        o_gate = self._sigmoid(combined[:, 2*self.lstm_size:3*self.lstm_size])  # Output gate
        g_gate = np.tanh(combined[:, 3*self.lstm_size:])  # Candidate values
        
        # Update cell state
        self.lstm_cell = f_gate * self.lstm_cell + i_gate * g_gate
        
        # Update hidden state
        self.lstm_hidden = o_gate * np.tanh(self.lstm_cell)
        
        return self.lstm_hidden
    
    def _multi_head_attention(self, x: np.ndarray) -> np.ndarray:
        """Multi-head attention mechanism."""
        attention_outputs = []
        
        for head in self.attention_weights:
            # Compute query, key, value
            query = np.dot(x, head['query'])
            key = np.dot(x, head['key'])
            value = np.dot(x, head['value'])
            
            # Attention scores
            scores = np.dot(query, key.T) / np.sqrt(key.shape[-1])
            attention_weights = self._softmax(scores)
            
            # Apply attention
            attended = np.dot(attention_weights, value)
            attention_outputs.append(attended)
        
        # Concatenate all heads
        return np.concatenate(attention_outputs, axis=-1)
    
    def _calculate_curiosity_bonus(self, state: np.ndarray, action: int, next_state: np.ndarray) -> float:
        """Calculate intrinsic motivation bonus."""
        # Encode states
        state_encoded = self._relu(np.dot(state, self.curiosity_encoder) + self.curiosity_bias)
        next_state_encoded = self._relu(np.dot(next_state, self.curiosity_encoder) + self.curiosity_bias)
        
        # Predict next state
        action_one_hot = np.zeros(self.action_count)
        action_one_hot[action] = 1
        forward_input = np.concatenate([state_encoded.flatten(), action_one_hot])
        predicted_next = self._relu(np.dot(forward_input.reshape(1, -1), self.forward_model) + self.forward_bias)
        
        # Prediction error as curiosity bonus
        prediction_error = np.mean((predicted_next - next_state_encoded) ** 2)
        return min(prediction_error * self.exploration_bonus, 1.0)
    
    def forward(self, state_vector: np.ndarray, training: bool = False) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Advanced forward pass with memory and attention."""
        if state_vector.ndim == 1:
            state_vector = state_vector.reshape(1, -1)

        # Input processing with batch normalization
        x = self._batch_normalize(state_vector, self.input_norm, training)
        x = self._relu(np.dot(x, self.input_weights) + self.input_bias)

        # LSTM for temporal processing
        lstm_out = self._lstm_forward(x)

        # Multi-head attention
        attended = self._multi_head_attention(lstm_out)

        # Actor network (policy)
        actor_h1 = self._relu(np.dot(attended, self.actor_hidden1) + self.actor_bias1)
        actor_h1 = self._dropout(actor_h1, 0.1, training)
        actor_h2 = self._relu(np.dot(actor_h1, self.actor_hidden2) + self.actor_bias2)

        # Action and vocabulary outputs
        action_logits = np.dot(actor_h2, self.action_output) + self.action_output_bias
        action_probs = self._softmax(action_logits)

        vocab_logits = np.dot(actor_h2, self.vocab_output) + self.vocab_output_bias
        vocab_probs = self._sigmoid(vocab_logits)

        # Critic network (value estimation)
        critic_h1 = self._relu(np.dot(attended, self.critic_hidden1) + self.critic_bias1)
        critic_h1 = self._dropout(critic_h1, 0.1, training)
        critic_h2 = self._relu(np.dot(critic_h1, self.critic_hidden2) + self.critic_bias2)
        value = np.dot(critic_h2, self.value_output) + self.value_output_bias

        return action_probs, vocab_probs, value.flatten()
    
    def predict_with_exploration(self, state_vector: np.ndarray) -> Tuple[int, List[int]]:
        """Advanced prediction with curiosity-driven exploration."""
        action_probs, vocab_probs, value = self.forward(state_vector, training=False)
        
        # State-based exploration bonus
        state_key = tuple(np.round(state_vector.flatten(), 2))
        visit_count = self.state_visit_counts.get(state_key, 0)
        exploration_bonus = 1.0 / (1.0 + visit_count)
        
        # UCB-style action selection
        action_values = action_probs.flatten() + exploration_bonus * np.sqrt(np.log(max(1, sum(self.state_visit_counts.values()))) / (1 + visit_count))
        selected_action = np.argmax(action_values)
        
        # Update visit count
        self.state_visit_counts[state_key] = visit_count + 1
        
        # Advanced vocabulary selection with temperature
        temperature = 0.8
        vocab_probs_temp = vocab_probs.flatten() ** (1.0 / temperature)
        vocab_probs_temp /= np.sum(vocab_probs_temp)
        
        # Sample multiple words based on probabilities
        selected_vocab = []
        for i, prob in enumerate(vocab_probs_temp):
            if prob > 0.3 or (prob > 0.1 and random.random() < prob):
                selected_vocab.append(i)
        
        # Ensure at least one word if none selected
        if not selected_vocab:
            selected_vocab = [np.argmax(vocab_probs_temp)]
        
        return selected_action, selected_vocab[:5]  # Limit to 5 words
    
    def _relu(self, x):
        return np.maximum(0, x)
    
    def _sigmoid(self, x):
        return 1 / (1 + np.exp(-np.clip(x, -250, 250)))
    
    def _softmax(self, x):
        exp_x = np.exp(x - np.max(x, axis=-1, keepdims=True))
        return exp_x / np.sum(exp_x, axis=-1, keepdims=True)
    
    def _dropout(self, x, rate, training):
        if training and rate > 0:
            mask = np.random.binomial(1, 1-rate, x.shape) / (1-rate)
            return x * mask
        return x
    
    def reset_memory(self):
        """Reset LSTM memory for new episode."""
        self.lstm_hidden = np.zeros((1, self.lstm_size))
        self.lstm_cell = np.zeros((1, self.lstm_size))
    
    def learn_advanced(self, batch_size: int = 64):
        """Advanced learning with Actor-Critic and GAE."""
        if len(self.episodic_memory) < batch_size:
            return

        # Sample batch from episodic memory
        batch = random.sample(list(self.episodic_memory), batch_size)

        # Prepare batch data
        states = np.array([exp['state'] for exp in batch])
        actions = np.array([exp['action'] for exp in batch])
        rewards = np.array([exp['reward'] for exp in batch])
        next_states = np.array([exp['next_state'] for exp in batch])
        dones = np.array([exp['done'] for exp in batch])

        # Forward pass for current and next states
        _, _, values = self.forward(states, training=True)
        _, _, next_values = self.forward(next_states, training=False)

        # Calculate advantages using GAE
        advantages = self._calculate_gae(rewards, values, next_values, dones)

        # Calculate returns
        returns = advantages + values

        # Actor-Critic loss and updates
        self._update_actor_critic(states, actions, advantages, returns)

        # Update curiosity model
        self._update_curiosity_model(states, actions, next_states)

        # Track learning progress
        avg_advantage = np.mean(np.abs(advantages))
        self.learning_progress.append(avg_advantage)

    def _calculate_gae(self, rewards, values, next_values, dones):
        """Calculate Generalized Advantage Estimation."""
        advantages = np.zeros_like(rewards)
        gae = 0

        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_value = next_values[t] * (1 - dones[t])
            else:
                next_value = values[t + 1]

            delta = rewards[t] + self.gamma * next_value - values[t]
            gae = delta + self.gamma * self.gae_lambda * gae * (1 - dones[t])
            advantages[t] = gae

        return advantages

    def _update_actor_critic(self, states, actions, advantages, returns):
        """Update actor and critic networks."""
        # This is a simplified version - in practice you'd use proper gradients
        # For now, we'll use the existing backpropagation with enhanced targets

        batch_size = states.shape[0]

        # Get current predictions
        action_probs, vocab_probs, values = self.forward(states, training=True)

        # Actor loss (policy gradient)
        action_targets = action_probs.copy()
        for i in range(batch_size):
            action_targets[i, actions[i]] += self.learning_rate * advantages[i]

        # Critic loss (value function)
        value_targets = returns.reshape(-1, 1)

        # Update networks (simplified - would use proper gradient descent)
        self._simplified_update(states, action_targets, vocab_probs, value_targets)

    def _update_curiosity_model(self, states, actions, next_states):
        """Update curiosity/forward model."""
        # Simplified curiosity model update
        for i in range(len(states)):
            curiosity_bonus = self._calculate_curiosity_bonus(
                states[i:i+1], actions[i], next_states[i:i+1]
            )
            self.intrinsic_motivation = 0.9 * self.intrinsic_motivation + 0.1 * curiosity_bonus

    def _simplified_update(self, states, action_targets, vocab_targets, value_targets):
        """Simplified network update (placeholder for full implementation)."""
        # This would be replaced with proper gradient-based optimization
        # For now, using the existing update mechanism with enhancements
        pass

    def remember_advanced(self, state, action, vocab_selection, reward, next_state, done,
                         intrinsic_reward=0.0, attention_weights=None):
        """Advanced memory storage with additional information."""
        experience = {
            'state': state.copy(),
            'action': action,
            'vocab_selection': vocab_selection.copy() if vocab_selection else [],
            'reward': reward,
            'intrinsic_reward': intrinsic_reward,
            'next_state': next_state.copy(),
            'done': done,
            'timestamp': len(self.episodic_memory),
            'attention_weights': attention_weights
        }

        self.episodic_memory.append(experience)
        self.working_memory.append(experience)

        # Update episode rewards
        total_reward = reward + intrinsic_reward
        if done:
            episode_reward = sum(exp['reward'] + exp.get('intrinsic_reward', 0)
                               for exp in self.working_memory)
            self.episode_rewards.append(episode_reward)
            self.working_memory.clear()

    def save_advanced_model(self, filepath: str):
        """Save advanced model with all components."""
        model_data = {
            # Basic network components
            'input_weights': self.input_weights,
            'input_bias': self.input_bias,
            'input_norm': self.input_norm,

            # LSTM components
            'lstm_weights_input': self.lstm_weights_input,
            'lstm_weights_hidden': self.lstm_weights_hidden,
            'lstm_bias': self.lstm_bias,
            'lstm_hidden': self.lstm_hidden,
            'lstm_cell': self.lstm_cell,

            # Attention components
            'attention_weights': self.attention_weights,

            # Actor network
            'actor_hidden1': self.actor_hidden1,
            'actor_bias1': self.actor_bias1,
            'actor_hidden2': self.actor_hidden2,
            'actor_bias2': self.actor_bias2,
            'action_output': self.action_output,
            'action_output_bias': self.action_output_bias,
            'vocab_output': self.vocab_output,
            'vocab_output_bias': self.vocab_output_bias,

            # Critic network
            'critic_hidden1': self.critic_hidden1,
            'critic_bias1': self.critic_bias1,
            'critic_hidden2': self.critic_hidden2,
            'critic_bias2': self.critic_bias2,
            'value_output': self.value_output,
            'value_output_bias': self.value_output_bias,

            # Curiosity network
            'curiosity_encoder': self.curiosity_encoder,
            'curiosity_bias': self.curiosity_bias,
            'forward_model': self.forward_model,
            'forward_bias': self.forward_bias,

            # Learning parameters
            'learning_rate': self.learning_rate,
            'gamma': self.gamma,
            'gae_lambda': self.gae_lambda,

            # Performance tracking
            'episode_rewards': list(self.episode_rewards),
            'learning_progress': list(self.learning_progress),
            'state_visit_counts': self.state_visit_counts,
            'intrinsic_motivation': self.intrinsic_motivation
        }

        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)

    def load_advanced_model(self, filepath: str):
        """Load advanced model with all components."""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)

        # Restore all components
        for key, value in model_data.items():
            if hasattr(self, key):
                setattr(self, key, value)

    def get_performance_metrics(self) -> Dict:
        """Get detailed performance metrics."""
        return {
            'avg_episode_reward': np.mean(self.episode_rewards) if self.episode_rewards else 0,
            'exploration_rate': len(self.state_visit_counts),
            'intrinsic_motivation': self.intrinsic_motivation,
            'memory_usage': len(self.working_memory),
            'episodic_memory_size': len(self.episodic_memory),
            'learning_progress': np.mean(self.learning_progress[-100:]) if self.learning_progress else 0,
            'recent_performance': np.mean(self.episode_rewards[-10:]) if len(self.episode_rewards) >= 10 else 0
        }
